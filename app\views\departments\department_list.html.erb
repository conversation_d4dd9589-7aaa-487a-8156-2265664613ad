
<% content_for :head do %>
<%end%>

<% content_for :bottombody do %>
<%end%>

<style>
    .content{
        display: flex;
        flex-direction: column;
    }
    #tab-department-list li a.active{
        color: #2C7BE5 !important;
        font-weight: bold !important;
        font-size: 1rem !important;
    }
    table .stastus-badge{
        --falcon-badge-padding-x: 0.711111em !important;
        --falcon-badge-padding-y: 0.355555em !important;
        --falcon-badge-font-size: 0.75em !important;
        --falcon-badge-font-weight: 600 !important;
        --falcon-badge-color: #fff !important;
        --falcon-badge-border-radius: 0.25rem !important;
        display: inline-block !important;
        padding: var(--falcon-badge-padding-y) var(--falcon-badge-padding-x) !important;
        font-size: var(--falcon-badge-font-size) !important;
        font-weight: var(--falcon-badge-font-weight) !important;
        line-height: 1 !important;
    }
    .badge-soft-yellow{
        background: #fff1c4;
        color: #dfb302;
    }
    .r-count{
        font-weight: bold;
        color: #787878;
        margin-left: 4px;
    }
    table td {
        white-space: normal !important;
        overflow: visible !important;
        text-overflow: clip !important;
    }
    
    /* Styles cho collapsible subdepartments */
    .collapse-toggle:hover {
        color: #2C7BE5 !important;
    }
    
    .department-parent-row:hover {
        background-color: #f8f9fa !important;
    }
    
    .subdepartment-row {
        border-left: 3px solid #e3f2fd !important;
    }
    
    .subdepartment-row:hover {
        background-color: #e8f4f8 !important;
    }
    
    .badge {
        font-size: 0.75em;
        font-weight: 500;
    }
</style>

<div class="card" style="flex-grow: 1;">
  <div class="card-body p-3">
    <h4 class="mb-3"><%= lib_translate('List_Department')%></h4>
    <div class="d-flex align-items-center justify-content-between">
        <div class="d-flex justify-content-start align-items-center">
            <%= form_tag departments_get_department_by_stype_path(),method:'GET',id:'get-departments-form',remote:true,authenticity_token: true do %>
                <div class="form-control me-3" style="width: fit-content;">
                    <input type="text" onchange="onChangeSearch()" name="search" placeholder="Tên phòng ban" style="outline: none;border: none;min-width: 274px;"/>
                    <span class="fas fa-search" style="color:#999999"></span>
                </div>
                <input type="hidden" style="display:none;" name="stype">
                <input type="hidden" style="display:none;" name="per_page" value="10">
                <input type="hidden" style="display:none;" name="page" value="1">
            <%end%>

        </div>
        <div class="d-flex justify-content-end">
            <div>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modal-export">
                    <span class="fas fa-upload me-2"></span>
                    <span>Xuất danh sách đơn vị</span>
                </button>
            </div>
        </div>
    </div>
    <hr class="mt-2 navbar-vertical-divider">
    <div class="d-flex justify-content-between align-items-end" style="border-bottom: 2px solid var(--falcon-border-color);">
        <ul class="nav nav-tabs" id="tab-department-list" style="border-bottom: none;user-select: none;" role="tablist">
            <% @group_list.each_with_index do |group,index|%>
                <li class="nav-item">
                    <a class="nav-link" data-group="<%= group.id%>" onclick="clickGroup('<%= group.id%>')" style="cursor: pointer;color:#969FA9;font-weight: 600;font-size:16px;" data-bs-toggle="tab"><%= group.name%> <span class="r-count"><%= group.count%></span></a>
                </li>
            <%end%>
        </ul>
    </div>
    <div id="table-container" class="mt-2">
        <table class="table table-sm table-hover m-0 mt-1" id="table-deparments">
            <thead style="background: #E7EDF0;">
                <tr style="vertical-align: middle">
                    <th class="p-0 m-0" style="font-weight: 500;font-size: 14px;text-align: center;width: 55px;">#</th>
                    <th style="font-weight: 500;font-size: 14px;">Tên đơn vị</th>
                    <th style="font-weight: 500;font-size: 14px;">Email</th>
                    <th style="font-weight: 500;font-size: 14px;">Trưởng phòng</th>
                    <th style="font-weight: 500;font-size: 14px;">Văn phòng</th>
                    <th style="font-weight: 500;font-size: 14px;text-align: center;">Đơn vị chủ quản</th>
                    <th style="font-weight: 500;font-size: 14px;text-align: center;text-align: center;">Trạng thái</th>
                    <th style="font-weight: 500;font-size: 14px;text-align: center;">Số lượng nhân sự</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
        <div class="d-flex justify-content-between align-items-end mt-2">
            <div style="text-wrap-mode: nowrap;">
                <span>Hiển thị <span class="item_count">0</span> trên <span class="total_record">0</span><span>
            </div>
            <div class="d-flex align-items-center per-page-wrap" style="display: none !important;">
                <div class="pagin-wrap"></div>
                <div class='d-flex ms-3 align-items-center'>
                    <span class='me-2' style='text-wrap-mode: nowrap;'>Số lượng bản ghi</span>
                    <% per_pages = [10,20,50]%>
                    <div class='btn-group'>
                        <button class='btn btn-sm btn-outline-primary dropdown-toggle per_page' style='border-color: var(--falcon-bg-navbar-glass);' type='button' data-bs-toggle='dropdown' aria-haspopup='true' aria-expanded='false'></button>
                        <div class='dropdown-menu' style="min-width: fit-content;">
                            <%per_pages.each do |amount|%>
                                <a class='dropdown-item' style="cursor: pointer;" onclick="clickPerPage(<%=amount%>)"><%=amount%></a>
                            <%end%>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
  </div>
</div>

<div class="modal fade" id="modal-export" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document" style="max-width: 500px">
    <div class="modal-content position-relative">
      <div class="position-absolute top-0 end-0 mt-2 me-2 z-index-1">
        <button class="btn-close btn btn-sm btn-circle d-flex flex-center transition-base" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body p-0">
        <div class="rounded-top-lg py-2 ps-3 pe-6 pt-3">
          <h5>Xuất danh sách đơn vị</h5>
        </div>
        <div class="p-3 pb-0 pt-1">
          <form style="user-select: none;">
            <div class="form-check" style="width: fit-content;">
                <input class="form-check-input" style="cursor: pointer;" id="select-field-all" type="checkbox" onchange="ClickSelectAll(this)"/>
                <label class="form-check-label m-0" style="cursor: pointer;" for="select-field-all">Chọn tất cả</label>
            </div>
            <div id="field-wrap">
                <% DepartmentsHelper::EXPORT_FIELDS.each_with_index do |field,index|%>
                    <div class="form-check" style="width: fit-content;">
                        <input class="form-check-input" name="<%= field[:name]%>" style="cursor: pointer;" id="select-field-<%= index%>" <%= field[:disabled] ? 'disabled' : ''%> type="checkbox" <%= field[:checked] ? 'checked' : '' %>/>
                        <label class="form-check-label m-0" style="cursor: pointer;" for="select-field-<%= index%>"><%= field[:text]%></label>
                    </div>
                <%end%>
            </div>
            <div class="modal-footer p-1 mt-3">
                <button class="btn btn-secondary" type="button" data-bs-dismiss="modal">Đóng</button>
                <button class="btn btn-primary" type="button" onclick="clickExport()">Xác nhận</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
<a href="" target="_blank" class="d-none" id="redirect-details"></a>

<%= form_tag departments_export_excel_path(),method:'GET',id:'export-departments-form' do %>
    <input type="hidden" style="display:none;" name="export_fields">
<%end%>

<script>

    // click group by localstorage
    $(`a[data-group]`).toggleClass("active",false);
    var group = localStorage.getItem("tab-group");
    if(group){
        $(`a[data-group="${group}"]`).click();
        $(`a[data-group="${group}"]`).toggleClass("active",true);
    }else{
        $('a[data-group]:first').click();
        $('a[data-group]:first').toggleClass("active",true);
        group = $('a[data-group]:first').data("group");
        localStorage.setItem("tab-group",group);
    }

    function onChangeSearch() {
        let form = $("#get-departments-form");
        form.submit();
        showLoadding(true);
    }
    
    function ClickSelectAll(button) {
        let checked = button.checked;
        $("#field-wrap").find('input:checkbox').prop("checked",checked);
    }

    function clickGroup(stype) {
        localStorage.setItem("tab-group",stype);
        searchDepartment(stype,1,null);
    }

    function clickPerPage(per_page) {
        searchDepartment(null,1,per_page);
    }

    function clickButtonPage(page) {
        searchDepartment(null,page,null);
    }

    function searchDepartment(stype,page,per_page) {
        let form = $("#get-departments-form");
        if(stype){
            form.find('[name="stype"]').val(stype);
        }
        if(page){
            form.find('[name="page"]').val(page);
        }
        if(per_page){
            form.find('[name="per_page"]').val(per_page);
        }
        form.submit();
        showLoadding(true);
    }

    function clickDepartmentRow(id) {
        window.location.href = `<%=departments_department_details_path(lang:session[:lang])%>&department_id=${id}`;
    }

    function clickExport() {
        let fields = $("#field-wrap input[type='checkbox']:checked").map((index,element)=>{return element.name});
        fields = fields.toArray();
        if(fields.length == 0){
            alert("Chọn các cột cần xuất");
            return;
        }
        
        let form = $("#export-departments-form");
        form.find('input[name="export_fields"]').val(fields.join(","));
        form.submit();

        $("#modal-export").modal('hide');
    }

</script>


