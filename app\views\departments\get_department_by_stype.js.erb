
<%
    rows = []
    status_list = [
                    {status:"0",text:"Hoạt động",style:"success"},
                    {status:"1",text:"Dừng hoạt động",style:"secondary"},
                    {status:"",text:"<PERSON><PERSON><PERSON> chỉ",style:"danger"},
                    {status:"",text:"Cần điều động",style:"warning"},
                    {status:"",text:"Giải thể",style:"secondary"},
                    {status:"",text:"Cần tuyển",style:"yellow"}
                ]
    @departments.each_with_index do |department, index|
        status = status_list.select{|item| item[:status] == department.status}.first
        stt = (index + 1) + ((session[:page].to_i - 1) * session[:per_page].to_i)
        
        # Lấy subdepartments cho department này
        subdepartments = @subdepartments_by_parent[department.id] || []
        has_subdepartments = subdepartments.any?
        
        # Row chính cho department cha
        rows << "<tr class='department-parent-row' data-department-id='#{department.id}' style='vertical-align: middle;'>
                    <td>#{stt}</td>
                    <td class='pt-2 pb-2'>
                        <div class='d-flex align-items-center'>
                            #{if has_subdepartments
                                "<button class='btn btn-sm btn-link p-0 me-2 collapse-toggle' onclick='toggleSubdepartments(#{department.id})' style='color: #6c757d; border: none; background: none;'>
                                    <i class='fas fa-chevron-right' id='chevron-#{department.id}' style='font-size: 12px; transition: transform 0.2s;'></i>
                                </button>"
                              else
                                "<span style='width: 24px; display: inline-block;'></span>"
                              end}
                            <div onclick='clickDepartmentRow(#{department.id})' style='cursor: pointer; flex-grow: 1;'>
                                <p class='p-0 m-0' style='font-weight: 500;color: #454545;'>#{department.name}</p>
                                <p class='p-0 m-0' style='font-weight: 400;font-size: 14px;color: #767676;font-style: italic;line-height: 10px;'>#{department.name_en}</p>
                            </div>
                            #{if has_subdepartments
                                "<span class='badge bg-light text-dark ms-2' style='font-size: 11px;'>#{subdepartments.count} nhóm</span>"
                              end}
                        </div>
                    </td>
                    <td onclick='clickDepartmentRow(#{department.id})' style='cursor: pointer;'>#{department.email}</td>
                    <td onclick='clickDepartmentRow(#{department.id})' style='cursor: pointer;'>#{department.last_name} #{department.first_name}</td>
                    <td onclick='clickDepartmentRow(#{department.id})' style='cursor: pointer;'>#{department.office}</td>
                    <td style='text-align: center;' onclick='clickDepartmentRow(#{department.id})' style='cursor: pointer;'>#{department.org_name}</td>
                    <td style='text-align: center;' onclick='clickDepartmentRow(#{department.id})' style='cursor: pointer;'>
                        <span class='stastus-badge rounded-pill badge-soft-#{status[:style]}'>#{status[:text]}</span>
                    </td>
                    <td style='text-align: center;' onclick='clickDepartmentRow(#{department.id})' style='cursor: pointer;'>#{department.user_count}</td>
                </tr>"
        
        # Rows cho subdepartments
        if has_subdepartments
            subdepartments.each_with_index do |subdept, sub_index|
                sub_leader_name = subdept.leader_last_name.present? && subdept.leader_first_name.present? ? "#{subdept.leader_last_name} #{subdept.leader_first_name}" : "Chưa có"
                sub_deputy_name = subdept.deputy_last_name.present? && subdept.deputy_first_name.present? ? "#{subdept.deputy_last_name} #{subdept.deputy_first_name}" : "Chưa có"
                
                rows << "<tr class='subdepartment-row' data-parent-id='#{department.id}' style='display: none; background-color: #f8f9fa; vertical-align: middle;'>
                            <td style='color: #6c757d;'>#{stt}.#{sub_index + 1}</td>
                            <td class='pt-2 pb-2'>
                                <div class='d-flex align-items-center ps-4'>
                                    <i class='fas fa-arrow-turn-down-right me-2 text-muted' style='font-size: 12px;'></i>
                                    <div onclick='clickDepartmentRow(#{subdept.id})' style='cursor: pointer; flex-grow: 1;'>
                                        <p class='p-0 m-0' style='font-weight: 500;color: #495057;'>#{subdept.name}</p>
                                        <p class='p-0 m-0' style='font-weight: 400;font-size: 12px;color: #6c757d;'>#{subdept.name_en || 'Nhóm con'}</p>
                                    </div>
                                    <span class='badge bg-info text-white ms-2' style='font-size: 10px;'>Nhóm</span>
                                </div>
                            </td>
                            <td onclick='clickDepartmentRow(#{subdept.id})' style='cursor: pointer; color: #6c757d;'>#{subdept.email || '-'}</td>
                            <td onclick='clickDepartmentRow(#{subdept.id})' style='cursor: pointer;'>
                                <div class='small'>
                                    <div style='font-size: 12px;'><strong>TN:</strong> #{sub_leader_name}</div>
                                    <div class='text-muted' style='font-size: 11px;'><strong>PN:</strong> #{sub_deputy_name}</div>
                                </div>
                            </td>
                            <td onclick='clickDepartmentRow(#{subdept.id})' style='cursor: pointer; color: #6c757d;'>-</td>
                            <td style='text-align: center;' onclick='clickDepartmentRow(#{subdept.id})' style='cursor: pointer; color: #6c757d;'>-</td>
                            <td style='text-align: center;' onclick='clickDepartmentRow(#{subdept.id})' style='cursor: pointer;'>
                                <span class='badge bg-secondary' style='font-size: 10px;'>Nhóm con</span>
                            </td>
                            <td style='text-align: center;' onclick='clickDepartmentRow(#{subdept.id})' style='cursor: pointer;'>#{subdept.amount || 0}</td>
                        </tr>"
            end
        end
    end
%>

var tbody = $("#table-deparments").find("tbody");
tbody.html("");
tbody.append(`<%= rows.join("").html_safe%>`);

$(".per-page-wrap").show();
$(".per_page").html(<%= session[:per_page]%>);
$(".item_count").html(<%= @departments.size%>);
$(".total_record").html(<%= @total_records%>);
var paginWrap = $(".pagin-wrap");
paginWrap.html(`<%= render_pagination_style_1("clickButtonPage",session[:page],session[:total_pages], session[:per_page]).html_safe%>`);

// Function toggle subdepartments
if (typeof window.toggleSubdepartments !== 'function') {
    window.toggleSubdepartments = function(departmentId) {
        const chevron = document.getElementById(`chevron-${departmentId}`);
        const subdepartmentRows = document.querySelectorAll(`tr[data-parent-id="${departmentId}"]`);
        
        if (subdepartmentRows.length === 0) return;
        
        const isExpanded = subdepartmentRows[0].style.display !== 'none';
        
        subdepartmentRows.forEach(row => {
            row.style.display = isExpanded ? 'none' : 'table-row';
        });
        
        // Rotate chevron
        if (chevron) {
            chevron.style.transform = isExpanded ? 'rotate(0deg)' : 'rotate(90deg)';
        }
    };
}

showLoadding(false);
